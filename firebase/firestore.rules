rules_version = '2';

// ⚠️  WARNING: SIMPLIFIED RULES - TEMPORARY ⚠️
// 
// These are simplified rules for development. 
// TODO: Add proper security rules before production deployment.
//
// Current approach:
// - Authenticated users can read/write most data
// - Global configurations remain read-only
// - Basic admin access controls preserved
// - Tenant isolation relaxed but maintained

service cloud.firestore {
  match /databases/{database}/documents {
    
    // ===============================
    // HELPER FUNCTIONS
    // ===============================
    
    // Basic authentication check
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Admin checks (preserved for future use)
    function isActiveAdmin(uid) {
      let admin = get(/databases/$(database)/documents/admin_users/$(uid)).data;
      return admin != null && admin.isActive == true;
    }
    
    function hasActiveTenantAccess(uid, tenantId) {
      let tenantAccess = get(/databases/$(database)/documents/admin_users/$(uid)/tenants/$(tenantId)).data;
      return tenantAccess != null && tenantAccess.isActive == true;
    }
    
    function isSuperAdmin(uid) {
      let tenantAccess = get(/databases/$(database)/documents/admin_users/$(uid)/tenants/fiaranow).data;
      return tenantAccess != null && tenantAccess.isActive == true && tenantAccess.role == 2;
    }

    // ===============================
    // SIMPLIFIED RULES
    // ===============================
    
    // Default rule: Allow authenticated users full access
    match /{document=**} {
      allow read, write: if isAuthenticated();
    }
    
    // ===============================
    // SPECIFIC RESTRICTIONS
    // ===============================
    
    // Global configurations - Read only
    match /global_configurations/{configId} {
      allow read: if true;
      allow write: if false;
    }
    
    // Admin users - Keep existing restrictions
    match /admin_users/{userId} {
      allow read: if isAuthenticated();
      allow create: if request.auth.uid == userId;
      allow update: if request.auth.uid == userId || isActiveAdmin(request.auth.uid);
      allow delete: if false;
      
      // Admin tenant access - Super admin only
      match /tenants/{tenantId} {
        allow read: if request.auth.uid == userId;
        allow write: if isSuperAdmin(request.auth.uid);
      }
    }
    
    // Prevent deletion of critical documents
    match /tenants/{tenantId} {
      allow read, create, update: if isAuthenticated();
      allow delete: if false;
    }
    
    match /payments/{paymentId} {
      allow read, create, update: if isAuthenticated();
      allow delete: if false;
    }
    
    match /tenants/{tenantId}/payments/{paymentId} {
      allow read, create, update: if isAuthenticated();
      allow delete: if false;
    }
    
    // Mobile user notifications - Backend managed
    match /tenants/{tenantId}/mobile_user_notifications/{notificationId} {
      allow read, update: if isAuthenticated();
      allow create, delete: if false;
    }
    
    // Feedback statistics - Backend managed
    match /tenants/{tenantId}/feedback_statistics/{statId} {
      allow read: if isAuthenticated();
      allow write: if false;
    }
    
    match /tenants/{tenantId}/feedback_events/{eventId} {
      allow read: if isAuthenticated();
      allow write: if false;
    }
  }
}